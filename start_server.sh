#!/bin/bash

# PPStructureV3 OCR服务启动脚本
# 使用方法: ./start_server.sh [port]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认端口
DEFAULT_PORT=9999
PORT=${1:-$DEFAULT_PORT}

echo -e "${BLUE}=== PPStructureV3 OCR服务启动脚本 ===${NC}"
echo -e "${BLUE}服务端口: ${PORT}${NC}"
echo ""

# 检查Python版本
echo -e "${YELLOW}检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到Python3，请先安装Python3${NC}"
    exit 1
fi

PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
echo -e "${GREEN}Python版本: ${PYTHON_VERSION}${NC}"

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}创建虚拟环境...${NC}"
    python3 -m venv venv
    echo -e "${GREEN}虚拟环境创建完成${NC}"
fi

# 激活虚拟环境
echo -e "${YELLOW}激活虚拟环境...${NC}"
source venv/bin/activate

# 跳过依赖更新，直接验证关键依赖
echo -e "${YELLOW}验证关键依赖...${NC}"
python3 -c "import flask; print('Flask: OK')" 2>/dev/null || echo -e "${RED}Flask未安装，请先运行: pip install -r requirements.txt${NC}"
python3 -c "from paddleocr import PPStructureV3; print('PP-StructureV3: OK')" 2>/dev/null || echo -e "${YELLOW}PP-StructureV3未安装或有问题，请先运行: pip install -r requirements.txt${NC}"

# 设置环境变量
export FLASK_APP=pp_structure_v3_server.py
export FLASK_ENV=development
export PADDLEX_AUTO_UPGRADE=False

# 启动服务
echo ""
echo -e "${GREEN}=== 启动PPStructureV3 OCR服务 ===${NC}"
echo -e "${GREEN}服务地址: http://localhost:${PORT}${NC}"
echo -e "${GREEN}API文档: http://localhost:${PORT}${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
echo ""

# 启动Flask应用
python3 pp_structure_v3_server.py --port ${PORT}
