2025-07-22 12:51:19 - pp_structure_v3 - INFO - <module>:441 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:51:19 - pp_structure_v3 - INFO - <module>:442 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:51:19 - pp_structure_v3 - INFO - <module>:443 - API文档:
2025-07-22 12:51:19 - pp_structure_v3 - INFO - <module>:444 -   GET  /              - 服务信息
2025-07-22 12:51:19 - pp_structure_v3 - INFO - <module>:445 -   GET  /health        - 健康检查
2025-07-22 12:51:19 - pp_structure_v3 - INFO - <module>:446 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:51:19 - pp_structure_v3 - INFO - <module>:447 -   POST /api/v1/structure/url   - URL图片结构化解析
2025-07-22 12:51:19 - pp_structure_v3 - INFO - <module>:448 -   POST /api/v1/structure/local - 本地文件结构化解析
2025-07-22 12:51:19 - pp_structure_v3 - INFO - init_model:79 - 开始初始化OCR模型...
2025-07-22 12:51:26 - pp_structure_v3 - INFO - init_model:87 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:51:27 - pp_structure_v3 - INFO - <module>:441 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:51:27 - pp_structure_v3 - INFO - <module>:442 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:51:27 - pp_structure_v3 - INFO - <module>:443 - API文档:
2025-07-22 12:51:27 - pp_structure_v3 - INFO - <module>:444 -   GET  /              - 服务信息
2025-07-22 12:51:27 - pp_structure_v3 - INFO - <module>:445 -   GET  /health        - 健康检查
2025-07-22 12:51:27 - pp_structure_v3 - INFO - <module>:446 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:51:27 - pp_structure_v3 - INFO - <module>:447 -   POST /api/v1/structure/url   - URL图片结构化解析
2025-07-22 12:51:27 - pp_structure_v3 - INFO - <module>:448 -   POST /api/v1/structure/local - 本地文件结构化解析
2025-07-22 12:51:27 - pp_structure_v3 - INFO - init_model:79 - 开始初始化OCR模型...
2025-07-22 12:51:32 - pp_structure_v3 - INFO - init_model:87 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:53:55 - pp_structure_v3 - INFO - main:311 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:53:55 - pp_structure_v3 - INFO - main:312 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:53:55 - pp_structure_v3 - INFO - main:313 - API文档:
2025-07-22 12:53:55 - pp_structure_v3 - INFO - main:314 -   GET  /              - 服务信息
2025-07-22 12:53:55 - pp_structure_v3 - INFO - main:315 -   GET  /health        - 健康检查
2025-07-22 12:53:55 - pp_structure_v3 - INFO - main:316 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:53:55 - pp_structure_v3 - INFO - init_model:75 - 开始初始化OCR模型...
2025-07-22 12:54:01 - pp_structure_v3 - INFO - init_model:83 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:54:01 - pp_structure_v3 - INFO - main:311 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:54:01 - pp_structure_v3 - INFO - main:312 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:54:01 - pp_structure_v3 - INFO - main:313 - API文档:
2025-07-22 12:54:01 - pp_structure_v3 - INFO - main:314 -   GET  /              - 服务信息
2025-07-22 12:54:01 - pp_structure_v3 - INFO - main:315 -   GET  /health        - 健康检查
2025-07-22 12:54:01 - pp_structure_v3 - INFO - main:316 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:54:01 - pp_structure_v3 - INFO - init_model:75 - 开始初始化OCR模型...
2025-07-22 12:54:06 - pp_structure_v3 - INFO - init_model:83 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_request_info:82 - Request: GET http://localhost:9999/health
2025-07-22 12:54:24 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive'}
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.001s
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_request_info:82 - Request: GET http://localhost:9999/
2025-07-22 12:54:24 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive'}
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.000s
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_request_info:82 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-22 12:54:24 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Length': '0'}
2025-07-22 12:54:24 - pp_structure_v3 - WARNING - structure_file:227 - 请求中没有文件
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_response_info:90 - Response: Unknown
2025-07-22 12:54:24 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.001s
2025-07-22 12:54:57 - pp_structure_v3 - INFO - main:311 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:54:57 - pp_structure_v3 - INFO - main:312 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:54:57 - pp_structure_v3 - INFO - main:313 - API文档:
2025-07-22 12:54:57 - pp_structure_v3 - INFO - main:314 -   GET  /              - 服务信息
2025-07-22 12:54:57 - pp_structure_v3 - INFO - main:315 -   GET  /health        - 健康检查
2025-07-22 12:54:57 - pp_structure_v3 - INFO - main:316 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:54:57 - pp_structure_v3 - INFO - init_model:75 - 开始初始化OCR模型...
2025-07-22 12:55:02 - pp_structure_v3 - INFO - init_model:83 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:55:02 - pp_structure_v3 - INFO - main:311 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:55:02 - pp_structure_v3 - INFO - main:312 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:55:02 - pp_structure_v3 - INFO - main:313 - API文档:
2025-07-22 12:55:02 - pp_structure_v3 - INFO - main:314 -   GET  /              - 服务信息
2025-07-22 12:55:02 - pp_structure_v3 - INFO - main:315 -   GET  /health        - 健康检查
2025-07-22 12:55:02 - pp_structure_v3 - INFO - main:316 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:55:02 - pp_structure_v3 - INFO - init_model:75 - 开始初始化OCR模型...
2025-07-22 12:55:08 - pp_structure_v3 - INFO - init_model:83 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_request_info:82 - Request: GET http://localhost:9999/health
2025-07-22 12:55:24 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive'}
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.001s
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_request_info:82 - Request: GET http://localhost:9999/
2025-07-22 12:55:24 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive'}
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.000s
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_request_info:82 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-22 12:55:24 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Length': '0'}
2025-07-22 12:55:24 - pp_structure_v3 - WARNING - structure_file:227 - 请求中没有文件
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_response_info:90 - Response: Unknown
2025-07-22 12:55:24 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.001s
2025-07-22 12:56:13 - pp_structure_v3 - INFO - main:311 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:56:13 - pp_structure_v3 - INFO - main:312 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:56:13 - pp_structure_v3 - INFO - main:313 - API文档:
2025-07-22 12:56:13 - pp_structure_v3 - INFO - main:314 -   GET  /              - 服务信息
2025-07-22 12:56:13 - pp_structure_v3 - INFO - main:315 -   GET  /health        - 健康检查
2025-07-22 12:56:13 - pp_structure_v3 - INFO - main:316 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:56:13 - pp_structure_v3 - INFO - init_model:75 - 开始初始化OCR模型...
2025-07-22 12:56:19 - pp_structure_v3 - INFO - init_model:83 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:57:20 - pp_structure_v3 - INFO - main:311 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:57:20 - pp_structure_v3 - INFO - main:312 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:57:20 - pp_structure_v3 - INFO - main:313 - API文档:
2025-07-22 12:57:20 - pp_structure_v3 - INFO - main:314 -   GET  /              - 服务信息
2025-07-22 12:57:20 - pp_structure_v3 - INFO - main:315 -   GET  /health        - 健康检查
2025-07-22 12:57:20 - pp_structure_v3 - INFO - main:316 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:57:20 - pp_structure_v3 - INFO - init_model:75 - 开始初始化OCR模型...
2025-07-22 12:57:25 - pp_structure_v3 - INFO - init_model:83 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:57:25 - pp_structure_v3 - INFO - main:311 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 12:57:25 - pp_structure_v3 - INFO - main:312 - 服务地址: http://0.0.0.0:9999
2025-07-22 12:57:25 - pp_structure_v3 - INFO - main:313 - API文档:
2025-07-22 12:57:25 - pp_structure_v3 - INFO - main:314 -   GET  /              - 服务信息
2025-07-22 12:57:25 - pp_structure_v3 - INFO - main:315 -   GET  /health        - 健康检查
2025-07-22 12:57:25 - pp_structure_v3 - INFO - main:316 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 12:57:25 - pp_structure_v3 - INFO - init_model:75 - 开始初始化OCR模型...
2025-07-22 12:57:31 - pp_structure_v3 - INFO - init_model:83 - ✅ PaddleX OCR模型初始化成功
2025-07-22 12:57:45 - pp_structure_v3 - INFO - log_request_info:82 - Request: GET http://localhost:9999/
2025-07-22 12:57:45 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'Connection': 'keep-alive', 'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'Sec-Ch-Ua-Mobile': '?0', 'Sec-Ch-Ua-Platform': '"macOS"', 'Upgrade-Insecure-Requests': '1', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'Sec-Fetch-Site': 'none', 'Sec-Fetch-Mode': 'navigate', 'Sec-Fetch-User': '?1', 'Sec-Fetch-Dest': 'document', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9', 'Cookie': 'Idea-b1a97b12=5dcb1210-b89f-4e2e-8e91-1737f11b7a87; Idea-b1a97ed1=5f1dfbe6-6264-45c0-a58b-703cd5f48de2; Pwd-State=1; Admin-Token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJmSlNpdER4Z2gyUHhLNjhJTjIyeGU2eXJjcWJSUTQwciJ9.Swgu-jiCwKMJE6s-MHTNyydLepg0E5EyvQlSMUMaa70'}
2025-07-22 12:57:45 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 12:57:45 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.002s
2025-07-22 12:58:21 - pp_structure_v3 - INFO - log_request_info:82 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-22 12:58:21 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'User-Agent': 'Apifox/1.0.0 (https://apifox.com)', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJmSlNpdER4Z2gyUHhLNjhJTjIyeGU2eXJjcWJSUTQwciJ9.Swgu-jiCwKMJE6s-MHTNyydLepg0E5EyvQlSMUMaa70', 'Accept': '*/*', 'Host': 'localhost:9999', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive', 'Content-Type': 'multipart/form-data; boundary=--------------------------876604388617879983903854', 'Content-Length': '803124'}
2025-07-22 12:58:21 - pp_structure_v3 - INFO - structure_file:254 - 处理上传文件: **********-1105422x.png
2025-07-22 12:58:21 - pp_structure_v3 - INFO - predict:116 - 开始处理 1 个图像文件
2025-07-22 12:58:21 - pp_structure_v3 - DEBUG - predict:120 - 处理第 1/1 个图像: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpc8gnb9y7.png
2025-07-22 12:58:36 - pp_structure_v3 - INFO - predict:171 - 成功处理 1 个图像
2025-07-22 12:58:36 - pp_structure_v3 - INFO - structure_file:266 - 文件 **********-1105422x.png 解析完成
2025-07-22 12:58:36 - pp_structure_v3 - DEBUG - structure_file:287 - 清理临时文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpc8gnb9y7.png
2025-07-22 12:58:36 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 12:58:36 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 14.697s
2025-07-22 13:04:09 - pp_structure_v3 - INFO - main:323 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:04:09 - pp_structure_v3 - INFO - main:324 - 服务地址: http://0.0.0.0:9999
2025-07-22 13:04:09 - pp_structure_v3 - INFO - main:325 - API文档:
2025-07-22 13:04:09 - pp_structure_v3 - INFO - main:326 -   GET  /              - 服务信息
2025-07-22 13:04:09 - pp_structure_v3 - INFO - main:327 -   GET  /health        - 健康检查
2025-07-22 13:04:09 - pp_structure_v3 - INFO - main:328 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:04:09 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:04:28 - pp_structure_v3 - INFO - init_model:84 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:05:09 - pp_structure_v3 - INFO - main:360 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:05:09 - pp_structure_v3 - INFO - main:361 - 服务地址: http://0.0.0.0:9999
2025-07-22 13:05:09 - pp_structure_v3 - INFO - main:362 - API文档:
2025-07-22 13:05:09 - pp_structure_v3 - INFO - main:363 -   GET  /              - 服务信息
2025-07-22 13:05:09 - pp_structure_v3 - INFO - main:364 -   GET  /health        - 健康检查
2025-07-22 13:05:09 - pp_structure_v3 - INFO - main:365 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:05:09 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:05:26 - pp_structure_v3 - INFO - init_model:84 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:06:05 - pp_structure_v3 - INFO - main:360 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:06:05 - pp_structure_v3 - INFO - main:361 - 服务地址: http://0.0.0.0:9999
2025-07-22 13:06:05 - pp_structure_v3 - INFO - main:362 - API文档:
2025-07-22 13:06:05 - pp_structure_v3 - INFO - main:363 -   GET  /              - 服务信息
2025-07-22 13:06:05 - pp_structure_v3 - INFO - main:364 -   GET  /health        - 健康检查
2025-07-22 13:06:05 - pp_structure_v3 - INFO - main:365 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:06:05 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:06:22 - pp_structure_v3 - INFO - init_model:84 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:07:05 - pp_structure_v3 - INFO - main:360 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:07:05 - pp_structure_v3 - INFO - main:361 - 服务地址: http://0.0.0.0:9999
2025-07-22 13:07:05 - pp_structure_v3 - INFO - main:362 - API文档:
2025-07-22 13:07:05 - pp_structure_v3 - INFO - main:363 -   GET  /              - 服务信息
2025-07-22 13:07:05 - pp_structure_v3 - INFO - main:364 -   GET  /health        - 健康检查
2025-07-22 13:07:05 - pp_structure_v3 - INFO - main:365 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:07:05 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:07:24 - pp_structure_v3 - INFO - init_model:84 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:07:31 - pp_structure_v3 - INFO - main:360 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:07:31 - pp_structure_v3 - INFO - main:361 - 服务地址: http://0.0.0.0:9998
2025-07-22 13:07:31 - pp_structure_v3 - INFO - main:362 - API文档:
2025-07-22 13:07:31 - pp_structure_v3 - INFO - main:363 -   GET  /              - 服务信息
2025-07-22 13:07:31 - pp_structure_v3 - INFO - main:364 -   GET  /health        - 健康检查
2025-07-22 13:07:31 - pp_structure_v3 - INFO - main:365 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:07:31 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:07:49 - pp_structure_v3 - INFO - init_model:84 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:07:49 - pp_structure_v3 - INFO - main:360 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:07:49 - pp_structure_v3 - INFO - main:361 - 服务地址: http://0.0.0.0:9998
2025-07-22 13:07:49 - pp_structure_v3 - INFO - main:362 - API文档:
2025-07-22 13:07:49 - pp_structure_v3 - INFO - main:363 -   GET  /              - 服务信息
2025-07-22 13:07:49 - pp_structure_v3 - INFO - main:364 -   GET  /health        - 健康检查
2025-07-22 13:07:49 - pp_structure_v3 - INFO - main:365 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:07:49 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:10:49 - pp_structure_v3 - INFO - main:298 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:10:49 - pp_structure_v3 - INFO - main:299 - 服务地址: http://0.0.0.0:9999
2025-07-22 13:10:49 - pp_structure_v3 - INFO - main:300 - API文档:
2025-07-22 13:10:49 - pp_structure_v3 - INFO - main:301 -   GET  /              - 服务信息
2025-07-22 13:10:49 - pp_structure_v3 - INFO - main:302 -   GET  /health        - 健康检查
2025-07-22 13:10:49 - pp_structure_v3 - INFO - main:303 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:10:49 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:11:01 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:11:03 - pp_structure_v3 - INFO - main:298 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:11:03 - pp_structure_v3 - INFO - main:299 - 服务地址: http://0.0.0.0:9999
2025-07-22 13:11:03 - pp_structure_v3 - INFO - main:300 - API文档:
2025-07-22 13:11:03 - pp_structure_v3 - INFO - main:301 -   GET  /              - 服务信息
2025-07-22 13:11:03 - pp_structure_v3 - INFO - main:302 -   GET  /health        - 健康检查
2025-07-22 13:11:03 - pp_structure_v3 - INFO - main:303 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:11:03 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:11:17 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_request_info:82 - Request: GET http://localhost:9999/health
2025-07-22 13:11:33 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive'}
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.000s
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_request_info:82 - Request: GET http://localhost:9999/
2025-07-22 13:11:33 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive'}
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.000s
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_request_info:82 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-22 13:11:33 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'Host': 'localhost:9999', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate, zstd', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Length': '0'}
2025-07-22 13:11:33 - pp_structure_v3 - WARNING - structure_file:214 - 请求中没有文件
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_response_info:90 - Response: Unknown
2025-07-22 13:11:33 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.001s
2025-07-22 13:20:13 - pp_structure_v3 - INFO - main:298 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:20:13 - pp_structure_v3 - INFO - main:299 - 服务地址: http://0.0.0.0:9999
2025-07-22 13:20:13 - pp_structure_v3 - INFO - main:300 - API文档:
2025-07-22 13:20:13 - pp_structure_v3 - INFO - main:301 -   GET  /              - 服务信息
2025-07-22 13:20:13 - pp_structure_v3 - INFO - main:302 -   GET  /health        - 健康检查
2025-07-22 13:20:13 - pp_structure_v3 - INFO - main:303 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:20:13 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:20:35 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:20:37 - pp_structure_v3 - INFO - main:298 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 13:20:37 - pp_structure_v3 - INFO - main:299 - 服务地址: http://0.0.0.0:9999
2025-07-22 13:20:37 - pp_structure_v3 - INFO - main:300 - API文档:
2025-07-22 13:20:37 - pp_structure_v3 - INFO - main:301 -   GET  /              - 服务信息
2025-07-22 13:20:37 - pp_structure_v3 - INFO - main:302 -   GET  /health        - 健康检查
2025-07-22 13:20:37 - pp_structure_v3 - INFO - main:303 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 13:20:37 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-22 13:20:53 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-22 13:21:04 - pp_structure_v3 - INFO - log_request_info:82 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-22 13:21:04 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'User-Agent': 'Apifox/1.0.0 (https://apifox.com)', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJmSlNpdER4Z2gyUHhLNjhJTjIyeGU2eXJjcWJSUTQwciJ9.Swgu-jiCwKMJE6s-MHTNyydLepg0E5EyvQlSMUMaa70', 'Accept': '*/*', 'Host': 'localhost:9999', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive', 'Content-Length': '0'}
2025-07-22 13:21:04 - pp_structure_v3 - WARNING - structure_file:214 - 请求中没有文件
2025-07-22 13:21:04 - pp_structure_v3 - INFO - log_response_info:90 - Response: Unknown
2025-07-22 13:21:04 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 0.008s
2025-07-22 13:21:21 - pp_structure_v3 - INFO - log_request_info:82 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-22 13:21:21 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'User-Agent': 'Apifox/1.0.0 (https://apifox.com)', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJmSlNpdER4Z2gyUHhLNjhJTjIyeGU2eXJjcWJSUTQwciJ9.Swgu-jiCwKMJE6s-MHTNyydLepg0E5EyvQlSMUMaa70', 'Accept': '*/*', 'Host': 'localhost:9999', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive', 'Content-Type': 'multipart/form-data; boundary=--------------------------831362547864656242595065', 'Content-Length': '803124'}
2025-07-22 13:21:21 - pp_structure_v3 - INFO - structure_file:241 - 处理上传文件: **********-1105422x.png
2025-07-22 13:21:21 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-22 13:21:21 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个图像: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpyso8o0un.png
2025-07-22 13:21:43 - pp_structure_v3 - INFO - predict:147 - PP-StructureV3成功处理 1 个图像
2025-07-22 13:21:43 - pp_structure_v3 - INFO - structure_file:253 - 文件 **********-1105422x.png 解析完成
2025-07-22 13:21:43 - pp_structure_v3 - DEBUG - structure_file:274 - 清理临时文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpyso8o0un.png
2025-07-22 13:21:43 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 13:21:43 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 21.891s
2025-07-22 13:23:14 - pp_structure_v3 - INFO - log_request_info:82 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-22 13:23:14 - pp_structure_v3 - DEBUG - log_request_info:83 - Headers: {'User-Agent': 'Apifox/1.0.0 (https://apifox.com)', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJmSlNpdER4Z2gyUHhLNjhJTjIyeGU2eXJjcWJSUTQwciJ9.Swgu-jiCwKMJE6s-MHTNyydLepg0E5EyvQlSMUMaa70', 'Accept': '*/*', 'Host': 'localhost:9999', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive', 'Content-Type': 'multipart/form-data; boundary=--------------------------805764561438107397933372', 'Content-Length': '459119'}
2025-07-22 13:23:14 - pp_structure_v3 - INFO - structure_file:241 - 处理上传文件: 1.pdf
2025-07-22 13:23:14 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-22 13:23:14 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个图像: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpllnw7pg1.pdf
2025-07-22 13:23:35 - pp_structure_v3 - INFO - predict:147 - PP-StructureV3成功处理 1 个图像
2025-07-22 13:23:35 - pp_structure_v3 - INFO - structure_file:253 - 文件 1.pdf 解析完成
2025-07-22 13:23:35 - pp_structure_v3 - DEBUG - structure_file:274 - 清理临时文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpllnw7pg1.pdf
2025-07-22 13:23:35 - pp_structure_v3 - INFO - log_response_info:90 - Response: 200
2025-07-22 13:23:35 - pp_structure_v3 - INFO - log_response_info:92 - Duration: 21.370s
2025-07-22 14:40:44 - pp_structure_v3 - INFO - main:298 - 启动PP-StructureV3文档结构化解析服务...
2025-07-22 14:40:44 - pp_structure_v3 - INFO - main:299 - 服务地址: http://0.0.0.0:9999
2025-07-22 14:40:44 - pp_structure_v3 - INFO - main:300 - API文档:
2025-07-22 14:40:44 - pp_structure_v3 - INFO - main:301 -   GET  /              - 服务信息
2025-07-22 14:40:44 - pp_structure_v3 - INFO - main:302 -   GET  /health        - 健康检查
2025-07-22 14:40:44 - pp_structure_v3 - INFO - main:303 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-22 14:40:44 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
