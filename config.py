# -*- coding: utf-8 -*-
"""
PPStructureV3服务配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""
    
    # 服务配置
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', 9999))
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'pdf'}
    
    # OCR模型配置
    USE_ANGLE_CLS = os.getenv('USE_ANGLE_CLS', 'True').lower() == 'true'
    OCR_LANG = os.getenv('OCR_LANG', 'ch')
    USE_GPU = os.getenv('USE_GPU', 'False').lower() == 'true'
    
    # PaddleX配置
    PADDLEX_AUTO_UPGRADE = os.getenv('PADDLEX_AUTO_UPGRADE', 'False')
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'pp_structure_v3.log')
    LOG_MAX_BYTES = int(os.getenv('LOG_MAX_BYTES', 10 * 1024 * 1024))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', 5))
    
    # 性能配置
    ENABLE_CACHE = os.getenv('ENABLE_CACHE', 'False').lower() == 'true'
    CACHE_TTL = int(os.getenv('CACHE_TTL', 3600))  # 1小时
    
    # 安全配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    ENABLE_CORS = os.getenv('ENABLE_CORS', 'True').lower() == 'true'
    
    @staticmethod
    def init_app(app):
        """初始化Flask应用配置"""
        # 创建上传目录
        upload_dir = Config.UPLOAD_FOLDER
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
