@echo off
REM PPStructureV3依赖修复脚本 (Windows版本)
REM 解决PaddleX版本问题和其他依赖冲突

echo 🔧 PPStructureV3依赖修复脚本
echo ================================

REM 激活虚拟环境
if exist "venv" (
    echo 激活虚拟环境...
    call venv\Scripts\activate.bat
) else (
    echo 创建虚拟环境...
    python -m venv venv
    call venv\Scripts\activate.bat
)

REM 升级pip
echo 升级pip...
python -m pip install --upgrade pip

REM 安装核心依赖
echo 安装核心依赖...
pip install Flask Flask-CORS requests python-dotenv Pillow numpy

REM 安装PaddlePaddle
echo 安装PaddlePaddle...
pip install paddlepaddle || pip install paddlepaddle-cpu

REM 安装PaddleOCR
echo 安装PaddleOCR...
pip install paddleocr

REM 尝试安装PaddleX（多个版本）
echo 尝试安装PaddleX...
pip install paddlex==3.1.3 || (
    echo ❌ PaddleX 3.1.3 安装失败，尝试其他版本...
    pip install paddlex==3.1.2 || (
        pip install paddlex==3.1.1 || (
            pip install paddlex==3.1.0 || (
                pip install paddlex==3.0.3 || (
                    pip install paddlex==3.0.2 || (
                        pip install paddlex==3.0.1 || (
                            pip install paddlex==3.0.0 || (
                                echo ⚠️ PaddleX安装失败，将使用PaddleOCR
                            )
                        )
                    )
                )
            )
        )
    )
)

REM 安装OpenCV
echo 安装OpenCV...
pip install opencv-python || pip install opencv-python-headless

REM 安装其他依赖
echo 安装其他依赖...
pip install psutil pyyaml 2>nul || echo 可选依赖安装失败，继续...

REM 验证安装
echo 验证安装...
python -c "import flask; print('✅ Flask OK')"
python -c "import paddleocr; print('✅ PaddleOCR OK')"
python -c "import cv2; print('✅ OpenCV OK')" 2>nul || echo ⚠️ OpenCV有问题
python -c "import paddlex; print('✅ PaddleX OK')" 2>nul || echo ⚠️ PaddleX未安装，将使用PaddleOCR

echo.
echo 🎉 依赖修复完成！
echo 现在可以运行: python pp_structure_v3_server.py
pause
