@echo off
REM PPStructureV3 OCR服务启动脚本 (Windows版本)
REM 使用方法: start_server.bat [port]

setlocal enabledelayedexpansion

REM 默认端口
set DEFAULT_PORT=9999
if "%1"=="" (
    set PORT=%DEFAULT_PORT%
) else (
    set PORT=%1
)

echo === PPStructureV3 OCR服务启动脚本 ===
echo 服务端口: %PORT%
echo.

REM 检查Python版本
echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Python版本: %PYTHON_VERSION%

REM 检查虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
    echo 虚拟环境创建完成
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 跳过依赖更新，直接验证关键依赖
echo 验证关键依赖...
python -c "import flask; print('Flask: OK')" 2>nul || echo Flask未安装，请先运行: pip install -r requirements.txt
python -c "from paddleocr import PPStructureV3; print('PP-StructureV3: OK')" 2>nul || echo PP-StructureV3未安装或有问题，请先运行: pip install -r requirements.txt

REM 设置环境变量
set FLASK_APP=pp_structure_v3_server.py
set FLASK_ENV=development
set PADDLEX_AUTO_UPGRADE=False

REM 启动服务
echo.
echo === 启动PPStructureV3 OCR服务 ===
echo 服务地址: http://localhost:%PORT%
echo API文档: http://localhost:%PORT%
echo 按 Ctrl+C 停止服务
echo.

REM 启动Flask应用
python pp_structure_v3_server.py --port %PORT%

pause
