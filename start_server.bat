@echo off
REM PPStructureV3 OCR服务启动脚本 (Windows版本)
REM 使用方法: start_server.bat [port]

setlocal enabledelayedexpansion

REM 默认端口
set DEFAULT_PORT=9999
if "%1"=="" (
    set PORT=%DEFAULT_PORT%
) else (
    set PORT=%1
)

echo === PPStructureV3 OCR服务启动脚本 ===
echo 服务端口: %PORT%
echo.

REM 检查Python版本
echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Python版本: %PYTHON_VERSION%

REM 检查虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
    echo 虚拟环境创建完成
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查并安装依赖
echo 检查依赖包...
if exist "requirements.txt" (
    echo 安装/更新依赖包...
    python -m pip install --upgrade pip
    pip install -r requirements.txt
    echo 依赖包安装完成
) else (
    echo 警告: 未找到requirements.txt文件
)

REM 检查必要的依赖
echo 验证关键依赖...
python -c "import flask; print('Flask: OK')" 2>nul || echo Flask未安装
python -c "import paddleocr; print('PaddleOCR: OK')" 2>nul || echo PaddleOCR未安装或有问题

REM 设置环境变量
set FLASK_APP=pp_structure_v3_server.py
set FLASK_ENV=development
set PADDLEX_AUTO_UPGRADE=False

REM 启动服务
echo.
echo === 启动PPStructureV3 OCR服务 ===
echo 服务地址: http://localhost:%PORT%
echo API文档: http://localhost:%PORT%
echo 按 Ctrl+C 停止服务
echo.

REM 启动Flask应用
python pp_structure_v3_server.py --port %PORT%

pause
