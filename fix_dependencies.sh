#!/bin/bash

# PPStructureV3依赖修复脚本
# 解决PaddleX版本问题和其他依赖冲突

set -e

echo "🔧 PPStructureV3依赖修复脚本"
echo "================================"

# 激活虚拟环境
if [ -d "venv" ]; then
    echo "激活虚拟环境..."
    source venv/bin/activate
else
    echo "创建虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
fi

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装核心依赖
echo "安装核心依赖..."
pip install Flask Flask-CORS requests python-dotenv Pillow numpy

# 安装PaddlePaddle
echo "安装PaddlePaddle..."
pip install paddlepaddle || pip install paddlepaddle-cpu

# 安装PaddleOCR
echo "安装PaddleOCR..."
pip install paddleocr

# 尝试安装PaddleX（多个版本）
echo "尝试安装PaddleX..."
PADDLEX_VERSIONS=("3.1.3" "3.1.2" "3.1.1" "3.1.0" "3.0.3" "3.0.2" "3.0.1" "3.0.0")

for version in "${PADDLEX_VERSIONS[@]}"; do
    echo "尝试安装PaddleX==$version..."
    if pip install paddlex==$version; then
        echo "✅ PaddleX $version 安装成功"
        break
    else
        echo "❌ PaddleX $version 安装失败，尝试下一个版本..."
    fi
done

# 安装OpenCV
echo "安装OpenCV..."
pip install opencv-python || pip install opencv-python-headless

# 安装其他依赖
echo "安装其他依赖..."
pip install psutil pyyaml || echo "可选依赖安装失败，继续..."

# 验证安装
echo "验证安装..."
python -c "import flask; print('✅ Flask OK')"
python -c "import paddleocr; print('✅ PaddleOCR OK')"
python -c "import cv2; print('✅ OpenCV OK')" || echo "⚠️ OpenCV有问题"
python -c "import paddlex; print('✅ PaddleX OK')" || echo "⚠️ PaddleX未安装，将使用PaddleOCR"

echo ""
echo "🎉 依赖修复完成！"
echo "现在可以运行: python pp_structure_v3_server.py"
