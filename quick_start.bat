@echo off
REM PP-StructureV3 快速启动脚本 (Windows版本)
REM 适用于已经配置好环境的情况，直接启动服务

setlocal enabledelayedexpansion

REM 默认端口
set DEFAULT_PORT=9999
if "%1"=="" (
    set PORT=%DEFAULT_PORT%
) else (
    set PORT=%1
)

echo === PP-StructureV3 快速启动 ===
echo 服务端口: %PORT%
echo.

REM 激活虚拟环境
if exist "venv" (
    echo 激活虚拟环境...
    call venv\Scripts\activate.bat
) else (
    echo 警告: 未找到虚拟环境，使用系统Python
)

REM 设置环境变量
set FLASK_APP=pp_structure_v3_server.py
set FLASK_ENV=development
set PADDLEX_AUTO_UPGRADE=False

REM 直接启动服务
echo.
echo === 启动PP-StructureV3服务 ===
echo 服务地址: http://localhost:%PORT%
echo 按 Ctrl+C 停止服务
echo.

REM 启动Flask应用
python pp_structure_v3_server.py --port %PORT%

pause
