#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPStructureV3依赖安装脚本
智能安装依赖，处理版本兼容性问题
"""
import subprocess
import sys
import os

def run_command(cmd, description=""):
    """运行命令并处理错误"""
    print(f"🔧 {description}")
    print(f"执行: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误信息: {e.stderr}")
        return False

def install_package(package, description=""):
    """安装单个包"""
    cmd = f"pip install {package}"
    return run_command(cmd, description or f"安装 {package}")

def main():
    """主安装流程"""
    print("🚀 PPStructureV3依赖安装脚本")
    print("=" * 50)
    
    # 升级pip
    print("\n1. 升级pip...")
    run_command("pip install --upgrade pip", "升级pip")
    
    # 核心依赖
    print("\n2. 安装核心依赖...")
    core_packages = [
        ("Flask", "Flask Web框架"),
        ("Flask-CORS", "CORS支持"),
        ("requests", "HTTP请求库"),
        ("python-dotenv", "环境变量支持"),
        ("Pillow", "图像处理库"),
        ("numpy", "数值计算库"),
    ]
    
    for package, desc in core_packages:
        install_package(package, desc)
    
    # OCR依赖
    print("\n3. 安装OCR相关依赖...")
    
    # 安装PaddlePaddle
    print("\n3.1 安装PaddlePaddle...")
    if not install_package("paddlepaddle", "PaddlePaddle深度学习框架"):
        print("⚠️  PaddlePaddle安装失败，尝试CPU版本...")
        install_package("paddlepaddle-cpu", "PaddlePaddle CPU版本")
    
    # 安装PaddleOCR
    print("\n3.2 安装PaddleOCR...")
    if not install_package("paddleocr", "PaddleOCR文字识别"):
        print("⚠️  PaddleOCR安装失败，请检查网络连接")

    # 测试PPStructureV3
    print("\n3.3 测试PPStructureV3...")
    try:
        import subprocess
        result = subprocess.run([
            "python", "-c",
            "from paddleocr import PPStructureV3; print('PPStructureV3可用')"
        ], capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print("✅ PPStructureV3可用")
        else:
            print("⚠️  PPStructureV3不可用，可能需要特定版本的paddleocr")
            print(f"错误信息: {result.stderr}")
    except Exception as e:
        print(f"⚠️  PPStructureV3测试失败: {e}")
    
    # 尝试安装PaddleX
    print("\n3.3 尝试安装PaddleX...")
    paddlex_versions = ["3.1.3", "3.1.2", "3.1.1", "3.1.0", "3.0.3", "3.0.2", "3.0.1", "3.0.0"]
    paddlex_installed = False
    
    for version in paddlex_versions:
        if install_package(f"paddlex=={version}", f"PaddleX {version}"):
            paddlex_installed = True
            break
    
    if not paddlex_installed:
        print("⚠️  PaddleX安装失败，服务将仅使用PaddleOCR")
    
    # 安装OpenCV
    print("\n4. 安装OpenCV...")
    opencv_packages = ["opencv-python", "opencv-python-headless"]
    opencv_installed = False
    
    for package in opencv_packages:
        if install_package(package, f"OpenCV ({package})"):
            opencv_installed = True
            break
    
    if not opencv_installed:
        print("⚠️  OpenCV安装失败，可能影响图像处理功能")
    
    # 可选依赖
    print("\n5. 安装可选依赖...")
    optional_packages = [
        ("psutil", "系统监控"),
        ("pyyaml", "YAML配置支持"),
        ("pytest", "测试框架"),
    ]
    
    for package, desc in optional_packages:
        install_package(package, desc)
    
    # 验证安装
    print("\n6. 验证安装...")
    verification_tests = [
        ("import flask", "Flask"),
        ("import requests", "Requests"),
        ("import PIL", "Pillow"),
        ("import numpy", "NumPy"),
        ("import cv2", "OpenCV"),
        ("import paddleocr", "PaddleOCR"),
        ("from paddleocr import PPStructureV3", "PPStructureV3"),
    ]
    
    for test_code, name in verification_tests:
        try:
            exec(test_code)
            print(f"✅ {name} 验证成功")
        except ImportError:
            print(f"❌ {name} 验证失败")
    
    # PaddleX验证（可选）
    try:
        import paddlex
        print(f"✅ PaddleX 验证成功 (版本: {paddlex.__version__})")
    except ImportError:
        print("⚠️  PaddleX 未安装，服务将使用PaddleOCR")
    
    print("\n" + "=" * 50)
    print("🎉 依赖安装完成！")
    print("\n💡 下一步:")
    print("1. 复制 .env.example 为 .env")
    print("2. 根据需要修改配置")
    print("3. 运行 python pp_structure_v3_server.py 启动服务")
    print("4. 运行 python quick_test.py 测试服务")

if __name__ == "__main__":
    main()
