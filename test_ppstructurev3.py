#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PPStructureV3功能的脚本
"""
import os
import sys
import json
import traceback
from pathlib import Path

def test_ppstructurev3_import():
    """测试PPStructureV3导入"""
    print("🔍 测试PPStructureV3导入...")
    try:
        from paddleocr import PPStructureV3
        print("✅ PPStructureV3导入成功")
        return True
    except ImportError as e:
        print(f"❌ PPStructureV3导入失败: {e}")
        print("💡 提示: 请确保安装了支持PPStructureV3的paddleocr版本")
        return False
    except Exception as e:
        print(f"❌ PPStructureV3导入异常: {e}")
        return False

def test_ppstructurev3_init():
    """测试PPStructureV3初始化"""
    print("\n🔍 测试PPStructureV3初始化...")
    try:
        from paddleocr import PPStructureV3
        
        pipeline = PPStructureV3(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False
        )
        print("✅ PPStructureV3初始化成功")
        return pipeline
    except Exception as e:
        print(f"❌ PPStructureV3初始化失败: {e}")
        traceback.print_exc()
        return None

def test_ppstructurev3_predict(pipeline, test_image_path):
    """测试PPStructureV3预测"""
    print(f"\n🔍 测试PPStructureV3预测: {test_image_path}")
    
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图片不存在: {test_image_path}")
        return None
    
    try:
        output = pipeline.predict(test_image_path)
        print(f"✅ PPStructureV3预测成功，返回 {len(output)} 个结果")
        
        # 分析结果结构
        for i, res in enumerate(output):
            print(f"\n📋 结果 {i+1}:")
            print(f"   类型: {type(res)}")
            print(f"   属性: {dir(res)}")
            
            # 尝试不同的方法获取结果
            if hasattr(res, 'print'):
                print("   调用print()方法:")
                try:
                    res.print()
                except Exception as e:
                    print(f"   print()方法失败: {e}")
            
            if hasattr(res, 'to_dict'):
                print("   调用to_dict()方法:")
                try:
                    result_dict = res.to_dict()
                    print(f"   结果: {json.dumps(result_dict, indent=2, ensure_ascii=False)}")
                except Exception as e:
                    print(f"   to_dict()方法失败: {e}")
            
            if hasattr(res, '_to_json'):
                print("   调用_to_json()方法:")
                try:
                    result_json = res._to_json()
                    print(f"   结果: {json.dumps(result_json, indent=2, ensure_ascii=False)}")
                except Exception as e:
                    print(f"   _to_json()方法失败: {e}")
            
            # 尝试保存结果
            if hasattr(res, 'save_to_json'):
                print("   尝试保存JSON:")
                try:
                    res.save_to_json(save_path="test_output")
                    print("   ✅ JSON保存成功")
                except Exception as e:
                    print(f"   JSON保存失败: {e}")
            
            if hasattr(res, 'save_to_markdown'):
                print("   尝试保存Markdown:")
                try:
                    res.save_to_markdown(save_path="test_output")
                    print("   ✅ Markdown保存成功")
                except Exception as e:
                    print(f"   Markdown保存失败: {e}")
        
        return output
        
    except Exception as e:
        print(f"❌ PPStructureV3预测失败: {e}")
        traceback.print_exc()
        return None

def find_test_image():
    """查找测试图片"""
    # 常见的图片文件扩展名
    image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif']
    
    # 在当前目录查找图片
    current_dir = Path('.')
    for ext in image_extensions:
        for pattern in [f'*{ext}', f'test*{ext}', f'sample*{ext}']:
            files = list(current_dir.glob(pattern))
            if files:
                return str(files[0])
    
    # 如果没找到，提示用户
    return None

def main():
    """主函数"""
    print("🚀 PPStructureV3功能测试")
    print("=" * 50)
    
    # 测试导入
    if not test_ppstructurev3_import():
        print("\n❌ 无法导入PPStructureV3，测试终止")
        print("💡 解决方案:")
        print("   1. 检查paddleocr版本是否支持PPStructureV3")
        print("   2. 尝试升级paddleocr: pip install --upgrade paddleocr")
        print("   3. 查看官方文档了解PPStructureV3的安装要求")
        return False
    
    # 测试初始化
    pipeline = test_ppstructurev3_init()
    if pipeline is None:
        print("\n❌ PPStructureV3初始化失败，测试终止")
        return False
    
    # 查找测试图片
    test_image = find_test_image()
    if test_image is None:
        print("\n⚠️  未找到测试图片")
        print("💡 请在当前目录放置一张图片文件（支持png, jpg, jpeg, bmp, tiff, gif格式）")
        print("   或者手动指定图片路径:")
        print("   python test_ppstructurev3.py /path/to/your/image.jpg")
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            test_image = sys.argv[1]
            print(f"📁 使用命令行指定的图片: {test_image}")
        else:
            return False
    else:
        print(f"📁 找到测试图片: {test_image}")
    
    # 测试预测
    output = test_ppstructurev3_predict(pipeline, test_image)
    if output is None:
        print("\n❌ PPStructureV3预测失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 PPStructureV3功能测试完成！")
    
    # 检查输出目录
    if os.path.exists("test_output"):
        print("📁 输出文件保存在 test_output 目录中")
        for file in os.listdir("test_output"):
            print(f"   - {file}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
