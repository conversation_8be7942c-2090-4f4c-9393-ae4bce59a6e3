# -*- coding: utf-8 -*-
"""
PPStructureV3 API测试脚本
"""
import requests
import json
import os
import time
import argparse
from pathlib import Path

class PPStructureV3APITester:
    def __init__(self, base_url="http://localhost:9999"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def test_health(self):
        """测试健康检查接口"""
        print("🔍 测试健康检查接口...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False
    
    def test_index(self):
        """测试服务信息接口"""
        print("\n🔍 测试服务信息接口...")
        try:
            response = self.session.get(f"{self.base_url}/")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ 服务信息获取失败: {e}")
            return False
    
    def test_file_upload(self, image_path):
        """测试文件上传接口"""
        print(f"\n🔍 测试文件上传接口: {image_path}")
        
        if not os.path.exists(image_path):
            print(f"❌ 测试图片不存在: {image_path}")
            return False
        
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
                response = self.session.post(f"{self.base_url}/api/v1/structure/file", files=files)
            
            print(f"状态码: {response.status_code}")
            result = response.json()
            print(f"响应状态: {result.get('status')}")
            print(f"消息: {result.get('message')}")
            
            if result.get('results'):
                print(f"解析结果数量: {len(result['results'])}")
                for i, res in enumerate(result['results']):
                    print(f"  结果 {i+1}: OCR文本数量 {len(res.get('ocr', []))}")
            
            return response.status_code == 200
        except Exception as e:
            print(f"❌ 文件上传测试失败: {e}")
            return False
    
    def test_url_processing(self, image_urls):
        """测试URL处理接口"""
        print(f"\n🔍 测试URL处理接口...")
        
        try:
            data = {"urls": image_urls}
            response = self.session.post(
                f"{self.base_url}/api/v1/structure/url",
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"状态码: {response.status_code}")
            result = response.json()
            print(f"响应状态: {result.get('status')}")
            print(f"消息: {result.get('message')}")
            
            if result.get('results'):
                print(f"解析结果数量: {len(result['results'])}")
                for i, res in enumerate(result['results']):
                    print(f"  结果 {i+1}: OCR文本数量 {len(res.get('ocr', []))}")
            
            return response.status_code == 200
        except Exception as e:
            print(f"❌ URL处理测试失败: {e}")
            return False
    
    def test_local_file_processing(self, local_paths):
        """测试本地文件处理接口"""
        print(f"\n🔍 测试本地文件处理接口...")
        
        # 检查文件是否存在
        missing_files = [path for path in local_paths if not os.path.exists(path)]
        if missing_files:
            print(f"❌ 以下文件不存在: {missing_files}")
            return False
        
        try:
            data = {"paths": local_paths}
            response = self.session.post(
                f"{self.base_url}/api/v1/structure/local",
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"状态码: {response.status_code}")
            result = response.json()
            print(f"响应状态: {result.get('status')}")
            print(f"消息: {result.get('message')}")
            
            if result.get('results'):
                print(f"解析结果数量: {len(result['results'])}")
                for i, res in enumerate(result['results']):
                    print(f"  结果 {i+1}: OCR文本数量 {len(res.get('ocr', []))}")
            
            return response.status_code == 200
        except Exception as e:
            print(f"❌ 本地文件处理测试失败: {e}")
            return False
    
    def test_error_cases(self):
        """测试错误情况"""
        print(f"\n🔍 测试错误情况...")
        
        # 测试无效端点
        try:
            response = self.session.get(f"{self.base_url}/invalid_endpoint")
            print(f"无效端点状态码: {response.status_code}")
            assert response.status_code == 404
        except Exception as e:
            print(f"❌ 无效端点测试失败: {e}")
            return False
        
        # 测试无文件上传
        try:
            response = self.session.post(f"{self.base_url}/api/v1/structure/file")
            print(f"无文件上传状态码: {response.status_code}")
            assert response.status_code == 400
        except Exception as e:
            print(f"❌ 无文件上传测试失败: {e}")
            return False
        
        # 测试空URL列表
        try:
            data = {"urls": []}
            response = self.session.post(
                f"{self.base_url}/api/v1/structure/url",
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            print(f"空URL列表状态码: {response.status_code}")
            assert response.status_code == 400
        except Exception as e:
            print(f"❌ 空URL列表测试失败: {e}")
            return False
        
        print("✅ 错误情况测试通过")
        return True
    
    def run_all_tests(self, test_image_path=None, test_urls=None, test_local_paths=None):
        """运行所有测试"""
        print("🚀 开始PPStructureV3 API测试")
        print(f"服务地址: {self.base_url}")
        print("=" * 50)
        
        results = []
        
        # 基础测试
        results.append(("健康检查", self.test_health()))
        results.append(("服务信息", self.test_index()))
        
        # 功能测试
        if test_image_path:
            results.append(("文件上传", self.test_file_upload(test_image_path)))
        
        if test_urls:
            results.append(("URL处理", self.test_url_processing(test_urls)))
        
        if test_local_paths:
            results.append(("本地文件", self.test_local_file_processing(test_local_paths)))
        
        # 错误测试
        results.append(("错误情况", self.test_error_cases()))
        
        # 总结
        print("\n" + "=" * 50)
        print("📊 测试结果总结:")
        passed = 0
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{len(results)} 个测试通过")
        return passed == len(results)

def main():
    parser = argparse.ArgumentParser(description='PPStructureV3 API测试工具')
    parser.add_argument('--url', default='http://localhost:9999', help='服务器URL')
    parser.add_argument('--image', help='测试图片路径')
    parser.add_argument('--test-urls', nargs='+', help='测试URL列表')
    parser.add_argument('--test-local', nargs='+', help='测试本地文件路径列表')
    
    args = parser.parse_args()
    
    tester = PPStructureV3APITester(args.url)
    
    # 如果没有指定测试图片，尝试查找当前目录下的图片
    test_image = args.image
    if not test_image:
        for ext in ['jpg', 'jpeg', 'png', 'bmp']:
            for pattern in [f'test.{ext}', f'sample.{ext}', f'*.{ext}']:
                files = list(Path('.').glob(pattern))
                if files:
                    test_image = str(files[0])
                    break
            if test_image:
                break
    
    success = tester.run_all_tests(
        test_image_path=test_image,
        test_urls=args.test_urls,
        test_local_paths=args.test_local
    )
    
    exit(0 if success else 1)

if __name__ == '__main__':
    main()
