# -*- coding: utf-8 -*-
"""
日志配置模块
"""
import logging
import logging.handlers
import os
from datetime import datetime
from config import Config

def setup_logger(name='pp_structure_v3', config=None):
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        config: 配置对象或字典

    Returns:
        logger: 配置好的日志记录器
    """
    if config is None:
        from config import Config
        config = Config()

    # 创建日志记录器
    logger = logging.getLogger(name)

    # 获取日志级别
    if hasattr(config, 'get'):
        # Flask配置对象
        log_level = config.get('LOG_LEVEL', 'INFO')
        log_file = config.get('LOG_FILE', 'pp_structure_v3.log')
        log_max_bytes = config.get('LOG_MAX_BYTES', 10 * 1024 * 1024)
        log_backup_count = config.get('LOG_BACKUP_COUNT', 5)
    else:
        # 配置类对象
        log_level = getattr(config, 'LOG_LEVEL', 'INFO')
        log_file = getattr(config, 'LOG_FILE', 'pp_structure_v3.log')
        log_max_bytes = getattr(config, 'LOG_MAX_BYTES', 10 * 1024 * 1024)
        log_backup_count = getattr(config, 'LOG_BACKUP_COUNT', 5)

    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（带轮转）
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)

        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=log_max_bytes,
            backupCount=log_backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, log_level.upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def log_request_info(logger, request):
    """记录请求信息"""
    logger.info(f"Request: {request.method} {request.url}")
    logger.debug(f"Headers: {dict(request.headers)}")
    if request.is_json:
        logger.debug(f"JSON Data: {request.get_json()}")

def log_response_info(logger, response, duration=None):
    """记录响应信息"""
    status_code = getattr(response, 'status_code', 'Unknown')
    logger.info(f"Response: {status_code}")
    if duration:
        logger.info(f"Duration: {duration:.3f}s")

def log_error(logger, error, context=None):
    """记录错误信息"""
    logger.error(f"Error: {str(error)}")
    if context:
        logger.error(f"Context: {context}")
    logger.exception("Exception details:")
