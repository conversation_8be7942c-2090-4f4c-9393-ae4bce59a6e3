#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPStructureV3服务快速测试脚本
"""
import requests
import json
import time
import sys

def test_service(base_url="http://localhost:9999"):
    """快速测试服务是否正常运行"""
    print("🚀 PPStructureV3服务快速测试")
    print(f"服务地址: {base_url}")
    print("-" * 40)
    
    try:
        # 测试服务是否启动
        print("1. 检查服务状态...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务正常运行")
            result = response.json()
            print(f"   模型状态: {result.get('model_status', 'unknown')}")
            print(f"   模型类型: {result.get('model_type', 'unknown')}")
        else:
            print(f"❌ 服务异常，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务，请确保服务已启动")
        print("   启动命令: python pp_structure_v3_server.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ 服务响应超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    try:
        # 测试服务信息
        print("\n2. 获取服务信息...")
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 服务信息获取成功")
            result = response.json()
            print(f"   服务版本: {result.get('version', 'unknown')}")
            print(f"   支持格式: {', '.join(result.get('config', {}).get('allowed_extensions', []))}")
        else:
            print(f"❌ 服务信息获取失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 服务信息测试失败: {e}")
    
    try:
        # 测试错误处理
        print("\n3. 测试错误处理...")
        response = requests.post(f"{base_url}/api/v1/structure/file", timeout=5)
        if response.status_code == 400:
            print("✅ 错误处理正常")
            result = response.json()
            print(f"   错误信息: {result.get('message', 'unknown')}")
        else:
            print(f"⚠️  错误处理异常，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
    
    print("\n" + "=" * 40)
    print("✅ 快速测试完成！")
    print("\n📖 使用说明:")
    print("1. 文件上传测试:")
    print("   curl -X POST -F 'file=@your_image.jpg' http://localhost:9999/api/v1/structure/file")
    print("\n2. URL图片测试:")
    print('   curl -X POST -H "Content-Type: application/json" \\')
    print('        -d \'{"urls":["http://example.com/image.jpg"]}\' \\')
    print('        http://localhost:9999/api/v1/structure/url')
    print("\n3. 完整API测试:")
    print("   python test_api.py")
    
    return True

def main():
    """主函数"""
    base_url = "http://localhost:9999"
    
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    success = test_service(base_url)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
