# PPStructureV3 OCR服务 Makefile

.PHONY: help install start test clean dev prod

# 默认目标
help:
	@echo "PPStructureV3 OCR服务管理命令:"
	@echo ""
	@echo "安装相关:"
	@echo "  install-smart - 智能依赖安装 (推荐)"
	@echo "  install       - 标准依赖安装"
	@echo "  install-core  - 核心依赖安装 (最小化)"
	@echo ""
	@echo "服务管理:"
	@echo "  start         - 启动服务 (开发模式)"
	@echo "  prod          - 启动服务 (生产模式)"
	@echo "  stop          - 停止服务"
	@echo ""
	@echo "测试相关:"
	@echo "  test          - 运行API测试"
	@echo "  quick-test    - 快速测试服务状态"
	@echo ""
	@echo "维护相关:"
	@echo "  clean         - 清理临时文件"
	@echo "  logs          - 查看日志"
	@echo "  check         - 检查环境和依赖"
	@echo "  config        - 创建配置文件"
	@echo ""

# 安装依赖
install:
	@echo "🔧 安装PPStructureV3服务..."
	python3 -m venv venv || python -m venv venv
	./venv/bin/pip install --upgrade pip || venv/Scripts/pip install --upgrade pip
	@echo "尝试完整安装..."
	./venv/bin/pip install -r requirements.txt || venv/Scripts/pip install -r requirements.txt || $(MAKE) install-core
	@echo "✅ 安装完成!"
	@echo "💡 提示: 复制 .env.example 为 .env 并根据需要修改配置"

# 核心依赖安装（备用方案）
install-core:
	@echo "🔧 安装核心依赖（备用方案）..."
	./venv/bin/pip install -r requirements-core.txt || venv/Scripts/pip install -r requirements-core.txt
	@echo "✅ 核心依赖安装完成!"

# 智能安装（推荐）
install-smart:
	@echo "🤖 智能依赖安装..."
	python3 -m venv venv || python -m venv venv
	./venv/bin/python install_dependencies.py || venv/Scripts/python install_dependencies.py

# 启动开发服务
start:
	@echo "🚀 启动PPStructureV3服务 (开发模式)..."
	./venv/bin/python pp_structure_v3_server.py --debug || venv/Scripts/python pp_structure_v3_server.py --debug

# 启动生产服务
prod:
	@echo "🚀 启动PPStructureV3服务 (生产模式)..."
	./venv/bin/python pp_structure_v3_server.py --config production || venv/Scripts/python pp_structure_v3_server.py --config production

# 运行完整测试
test:
	@echo "🧪 运行API测试..."
	./venv/bin/python test_api.py || venv/Scripts/python test_api.py

# 快速测试
quick-test:
	@echo "⚡ 快速测试服务状态..."
	./venv/bin/python quick_test.py || venv/Scripts/python quick_test.py

# 查看日志
logs:
	@if [ -f "logs/pp_structure_v3.log" ]; then \
		echo "📋 查看最新日志:"; \
		tail -f logs/pp_structure_v3.log; \
	elif [ -f "pp_structure_v3.log" ]; then \
		echo "📋 查看最新日志:"; \
		tail -f pp_structure_v3.log; \
	else \
		echo "❌ 未找到日志文件"; \
	fi

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	rm -rf __pycache__/
	rm -rf *.pyc
	rm -rf .pytest_cache/
	rm -rf uploads/*
	@echo "✅ 清理完成!"

# 停止服务 (通过进程名)
stop:
	@echo "🛑 停止PPStructureV3服务..."
	@pkill -f "pp_structure_v3_server.py" || echo "没有找到运行中的服务"

# 创建配置文件
config:
	@if [ ! -f ".env" ]; then \
		echo "📝 创建配置文件..."; \
		cp .env.example .env; \
		echo "✅ 已创建 .env 配置文件，请根据需要修改"; \
	else \
		echo "⚠️  .env 配置文件已存在"; \
	fi

# 检查依赖
check:
	@echo "🔍 检查环境和依赖..."
	@python3 --version || python --version
	@echo "虚拟环境状态:"
	@if [ -d "venv" ]; then echo "✅ 虚拟环境已创建"; else echo "❌ 虚拟环境未创建"; fi
	@echo "依赖检查:"
	@./venv/bin/python -c "import flask; print('✅ Flask已安装')" 2>/dev/null || venv/Scripts/python -c "import flask; print('✅ Flask已安装')" 2>/dev/null || echo "❌ Flask未安装"
	@./venv/bin/python -c "import paddleocr; print('✅ PaddleOCR已安装')" 2>/dev/null || venv/Scripts/python -c "import paddleocr; print('✅ PaddleOCR已安装')" 2>/dev/null || echo "⚠️  PaddleOCR未安装或有问题"

# 开发环境设置
dev-setup: install config
	@echo "🛠️  开发环境设置完成!"
	@echo "💡 下一步:"
	@echo "   1. 修改 .env 配置文件"
	@echo "   2. 运行 make start 启动服务"
	@echo "   3. 运行 make quick-test 测试服务"
