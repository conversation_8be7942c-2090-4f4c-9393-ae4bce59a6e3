#!/bin/bash

# PP-StructureV3 快速启动脚本
# 适用于已经配置好环境的情况，直接启动服务

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认端口
DEFAULT_PORT=9999
PORT=${1:-$DEFAULT_PORT}

echo -e "${BLUE}=== PP-StructureV3 快速启动 ===${NC}"
echo -e "${BLUE}服务端口: ${PORT}${NC}"
echo ""

# 激活虚拟环境
if [ -d "venv" ]; then
    echo -e "${GREEN}激活虚拟环境...${NC}"
    source venv/bin/activate
else
    echo "警告: 未找到虚拟环境，使用系统Python"
fi

# 设置环境变量
export FLASK_APP=pp_structure_v3_server.py
export FLASK_ENV=development
export PADDLEX_AUTO_UPGRADE=False

# 直接启动服务
echo ""
echo -e "${GREEN}=== 启动PP-StructureV3服务 ===${NC}"
echo -e "${GREEN}服务地址: http://localhost:${PORT}${NC}"
echo -e "${GREEN}按 Ctrl+C 停止服务${NC}"
echo ""

# 启动Flask应用
python3 pp_structure_v3_server.py --port ${PORT}
