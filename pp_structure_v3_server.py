# -*- coding: utf-8 -*-
"""
PP-StructureV3 OCR服务器，支持文档结构化解析
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import tempfile
import traceback
import time
import argparse
import json
from functools import wraps
from werkzeug.utils import secure_filename

# 导入配置和日志模块
from config import config, Config
from logger import setup_logger, log_request_info, log_response_info, log_error

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 启用CORS
    if app.config.get('ENABLE_CORS', True):
        CORS(app)
    
    return app

def allowed_file(filename, allowed_extensions):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def log_api_call(f):
    """API调用日志装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        log_request_info(logger, request)
        
        try:
            result = f(*args, **kwargs)
            duration = time.time() - start_time
            log_response_info(logger, result, duration)
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_error(logger, e, f"API: {f.__name__}, Duration: {duration:.3f}s")
            return jsonify({
                "status": "error",
                "message": f"服务内部错误: {str(e)}"
            }), 500
    
    return decorated_function

class PPStructureV3Service:
    def __init__(self):
        self.model = None
        self.model_type = None
        self.is_initialized = False
        
    def init_model(self):
        """初始化PP-StructureV3模型"""
        if self.is_initialized:
            return True

        try:
            logger.info("开始初始化PP-StructureV3模型...")

            # 只使用PP-StructureV3
            from paddleocr import PPStructureV3
            self.model = PPStructureV3(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False
            )
            self.model_type = "ppstructurev3"
            self.is_initialized = True
            logger.info("✅ PP-StructureV3模型初始化成功")
            return True

        except ImportError as e:
            logger.error(f"PP-StructureV3导入失败: {e}")
            logger.error("请确保安装了支持PP-StructureV3的paddleocr版本")
            return False
        except Exception as e:
            logger.error(f"PP-StructureV3初始化失败: {e}")
            log_error(logger, e, "PP-StructureV3初始化")
            return False
    
    def predict(self, image_paths):
        """PP-StructureV3预测"""
        if not self.is_initialized:
            if not self.init_model():
                raise Exception("PP-StructureV3模型初始化失败")

        try:
            logger.info(f"开始使用PP-StructureV3处理 {len(image_paths)} 个图像文件")
            results = []

            for i, image_path in enumerate(image_paths):
                logger.debug(f"处理第 {i+1}/{len(image_paths)} 个图像: {image_path}")

                # 使用PP-StructureV3进行预测
                output = self.model.predict(image_path)

                for res in output:
                    # 转换为JSON格式以便序列化
                    try:
                        # 尝试使用内置的转换方法
                        if hasattr(res, 'to_dict'):
                            result_dict = res.to_dict()
                        elif hasattr(res, '_to_json'):
                            result_dict = res._to_json()
                        else:
                            # 手动提取结果属性
                            result_dict = {}

                            # 尝试获取各种可能的属性
                            for attr in ['layout_result', 'ocr_result', 'table_result', 'formula_result',
                                        'chart_result', 'seal_result', 'image_path']:
                                if hasattr(res, attr):
                                    result_dict[attr] = getattr(res, attr)

                            # 如果没有找到任何属性，使用字符串表示
                            if not result_dict:
                                result_dict = {"raw_result": str(res)}

                        # 确保包含图像路径和模型类型
                        result_dict["image_path"] = image_path
                        result_dict["model_type"] = "ppstructurev3"
                        results.append(result_dict)

                    except Exception as e:
                        logger.warning(f"结果转换失败: {e}, 使用原始结果")
                        results.append({
                            "image_path": image_path,
                            "model_type": "ppstructurev3",
                            "raw_result": str(res),
                            "error": f"结果转换失败: {str(e)}"
                        })

            logger.info(f"PP-StructureV3成功处理 {len(results)} 个图像")
            return results

        except Exception as e:
            logger.error(f"PP-StructureV3预测失败: {e}")
            log_error(logger, e, f"预测图像: {image_paths}")
            raise Exception(f"PP-StructureV3预测失败: {e}")

# 创建PP-StructureV3服务实例
structure_service = PPStructureV3Service()

def create_routes(app):
    """创建所有路由"""
    
    @app.route('/')
    @log_api_call
    def index():
        """服务信息"""
        return jsonify({
            "message": "PP-StructureV3 Document Structure Analysis Service",
            "description": "基于PP-StructureV3的文档结构化解析服务，支持版面分析、表格识别、公式识别、图表识别等",
            "version": "1.0.0",
            "model": "PP-StructureV3",
            "model_status": "initialized" if structure_service.is_initialized else "not_initialized",
            "model_type": structure_service.model_type,
            "features": [
                "文档版面分析",
                "OCR文字识别",
                "表格结构识别",
                "数学公式识别",
                "图表识别",
                "印章检测"
            ],
            "endpoints": {
                "health": "/health",
                "structure_file": "/api/v1/structure/file (POST)",
                "structure_url": "/api/v1/structure/url (POST)",
                "structure_local": "/api/v1/structure/local (POST)"
            },
            "config": {
                "max_file_size": app.config.get('MAX_CONTENT_LENGTH'),
                "allowed_extensions": list(app.config.get('ALLOWED_EXTENSIONS', [])),
                "debug_mode": app.config.get('DEBUG', False)
            }
        })

    @app.route('/health')
    @log_api_call
    def health():
        """健康检查"""
        model_status = "healthy" if structure_service.is_initialized else "initializing"
        
        return jsonify({
            "status": "healthy",
            "service": "PP-StructureV3 Document Structure Analysis Service",
            "model": "PP-StructureV3",
            "model_status": model_status,
            "model_type": structure_service.model_type,
            "is_initialized": structure_service.is_initialized,
            "timestamp": time.time()
        })

    @app.route('/api/v1/structure/file', methods=['POST'])
    @log_api_call
    def structure_file():
        """处理上传的文件进行结构化解析"""
        if 'file' not in request.files:
            logger.warning("请求中没有文件")
            return jsonify({
                "status": "error",
                "message": "没有上传文件",
                "code": "NO_FILE"
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            logger.warning("上传的文件名为空")
            return jsonify({
                "status": "error",
                "message": "文件名为空",
                "code": "EMPTY_FILENAME"
            }), 400
        
        # 检查文件类型
        if not allowed_file(file.filename, app.config['ALLOWED_EXTENSIONS']):
            logger.warning(f"不支持的文件类型: {file.filename}")
            return jsonify({
                "status": "error",
                "message": f"不支持的文件类型，支持的格式: {', '.join(app.config['ALLOWED_EXTENSIONS'])}",
                "code": "UNSUPPORTED_FILE_TYPE"
            }), 400
        
        # 安全的文件名
        filename = secure_filename(file.filename)
        logger.info(f"处理上传文件: {filename}")
        
        # 保存临时文件
        temp_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as tmp_file:
                file.save(tmp_file.name)
                temp_path = tmp_file.name
            
            # 进行结构化解析
            results = structure_service.predict([temp_path])
            
            logger.info(f"文件 {filename} 解析完成")
            return jsonify({
                "status": "success",
                "message": "文档结构化解析完成",
                "filename": filename,
                "results": results
            })
            
        except Exception as e:
            logger.error(f"文件解析失败: {filename}, 错误: {str(e)}")
            return jsonify({
                "status": "error",
                "message": f"结构化解析失败: {str(e)}",
                "code": "PROCESSING_ERROR"
            }), 500
            
        finally:
            # 清理临时文件
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                    logger.debug(f"清理临时文件: {temp_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")

    return app

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PP-StructureV3 OCR服务器')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=9999, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--config', default='default', help='配置环境 (default/development/production)')
    
    args = parser.parse_args()
    
    # 创建应用
    app = create_app(args.config)
    global logger
    logger = setup_logger('pp_structure_v3', app.config)
    
    # 注册路由
    app = create_routes(app)
    
    logger.info("启动PP-StructureV3文档结构化解析服务...")
    logger.info(f"服务地址: http://{args.host}:{args.port}")
    logger.info("API文档:")
    logger.info("  GET  /              - 服务信息")
    logger.info("  GET  /health        - 健康检查")
    logger.info("  POST /api/v1/structure/file  - 上传文件结构化解析")
    
    # 预初始化模型（可选）
    if not structure_service.init_model():
        logger.warning("模型初始化失败，将在首次请求时重试")
    
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug or app.config.get('DEBUG', False)
    )

if __name__ == '__main__':
    main()
