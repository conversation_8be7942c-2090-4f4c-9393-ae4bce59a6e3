# -*- coding: utf-8 -*-
"""
PP-StructureV3 OCR服务器，支持文档结构化解析
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import tempfile
import json
import traceback
import time
import argparse
from functools import wraps
from werkzeug.utils import secure_filename

# 导入配置和日志模块
from config import config, Config
from logger import setup_logger, log_request_info, log_response_info, log_error

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 启用CORS
    if app.config.get('ENABLE_CORS', True):
        CORS(app)

    return app

app = create_app(os.getenv('FLASK_CONFIG', 'default'))
logger = setup_logger('pp_structure_v3', app.config)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def log_api_call(f):
    """API调用日志装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        log_request_info(logger, request)

        try:
            result = f(*args, **kwargs)
            duration = time.time() - start_time
            log_response_info(logger, result, duration)
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_error(logger, e, f"API: {f.__name__}, Duration: {duration:.3f}s")
            return jsonify({
                "status": "error",
                "message": f"服务内部错误: {str(e)}"
            }), 500

    return decorated_function

class PPStructureV3Service:
    def __init__(self):
        self.model = None
        self.model_type = None
        self.is_initialized = False

    def init_model(self):
        """初始化PP-StructureV3模型"""
        if self.is_initialized:
            return True

        try:
            # 设置环境变量阻止自动升级
            os.environ['PADDLEX_AUTO_UPGRADE'] = app.config.get('PADDLEX_AUTO_UPGRADE', 'False')

            logger.info("开始初始化OCR模型...")

            # 尝试使用PaddleX的OCR pipeline作为替代
            try:
                from paddlex import create_pipeline
                self.model = create_pipeline(pipeline="OCR")
                self.model_type = "paddlex"
                self.is_initialized = True
                logger.info("✅ PaddleX OCR模型初始化成功")
                return True
            except Exception as e1:
                logger.warning(f"PaddleX OCR初始化失败: {e1}")

                # 尝试使用PaddleOCR的基础OCR
                try:
                    from paddleocr import PaddleOCR
                    self.model = PaddleOCR(
                        use_angle_cls=app.config.get('USE_ANGLE_CLS', True),
                        lang=app.config.get('OCR_LANG', 'ch'),
                        use_gpu=app.config.get('USE_GPU', False)
                    )
                    self.model_type = "paddleocr"
                    self.is_initialized = True
                    logger.info("✅ PaddleOCR模型初始化成功")
                    return True
                except Exception as e2:
                    logger.error(f"PaddleOCR初始化失败: {e2}")
                    return False

        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            log_error(logger, e, "模型初始化")
            return False
    
    def predict(self, image_paths):
        """PP-StructureV3预测（使用OCR作为替代）"""
        if not self.is_initialized:
            if not self.init_model():
                raise Exception("模型初始化失败")

        try:
            logger.info(f"开始处理 {len(image_paths)} 个图像文件")
            results = []

            for i, image_path in enumerate(image_paths):
                logger.debug(f"处理第 {i+1}/{len(image_paths)} 个图像: {image_path}")

                if self.model_type == "paddlex":
                    # PaddleX风格
                    output = self.model.predict(
                        input=[image_path],
                        use_doc_orientation_classify=False,
                        use_doc_unwarping=False,
                        use_textline_orientation=False,
                    )

                    for res in output:
                        if hasattr(res, '_to_json'):
                            result = res._to_json()
                            # 模拟结构化解析结果
                            structured_result = {
                                "image_path": image_path,
                                "layout": [{"type": "text", "bbox": box} for box in result["res"].get("dt_polys", [])],
                                "ocr": result["res"].get("rec_text", []),
                                "table": [],  # 暂时为空
                                "confidence": result["res"].get("rec_score", []),
                                "raw_ocr_result": result["res"]
                            }
                            results.append(structured_result)
                        else:
                            results.append(res)

                elif self.model_type == "paddleocr":
                    # PaddleOCR风格
                    result = self.model.ocr(image_path, cls=True)
                    if result and result[0]:
                        # 模拟结构化解析结果
                        structured_result = {
                            "image_path": image_path,
                            "layout": [{"type": "text", "bbox": line[0]} for line in result[0] if line],
                            "ocr": [line[1][0] for line in result[0] if line],
                            "confidence": [line[1][1] for line in result[0] if line],
                            "table": [],  # 暂时为空
                            "raw_ocr_result": result
                        }
                    else:
                        structured_result = {
                            "image_path": image_path,
                            "layout": [],
                            "ocr": [],
                            "confidence": [],
                            "table": [],
                            "raw_ocr_result": result
                        }
                    results.append(structured_result)

            logger.info(f"成功处理 {len(results)} 个图像")
            return results

        except Exception as e:
            logger.error(f"结构化解析预测失败: {e}")
            log_error(logger, e, f"预测图像: {image_paths}")
            raise Exception(f"结构化解析预测失败: {e}")

# 创建PP-StructureV3服务实例
structure_service = PPStructureV3Service()

@app.route('/')
@log_api_call
def index():
    """服务信息"""
    return jsonify({
        "message": "PP-StructureV3 Document Structure Analysis Service",
        "description": "支持文档结构化解析，包括版面分析、表格识别、公式识别等",
        "version": "1.0.0",
        "model_status": "initialized" if structure_service.is_initialized else "not_initialized",
        "model_type": structure_service.model_type,
        "endpoints": {
            "health": "/health",
            "structure_file": "/api/v1/structure/file (POST)",
            "structure_url": "/api/v1/structure/url (POST)",
            "structure_local": "/api/v1/structure/local (POST)"
        },
        "config": {
            "max_file_size": app.config.get('MAX_CONTENT_LENGTH'),
            "allowed_extensions": list(app.config.get('ALLOWED_EXTENSIONS', [])),
            "debug_mode": app.config.get('DEBUG', False)
        }
    })

@app.route('/health')
@log_api_call
def health():
    """健康检查"""
    model_status = "healthy" if structure_service.is_initialized else "initializing"

    return jsonify({
        "status": "healthy",
        "service": "PP-StructureV3 Document Structure Analysis Service",
        "model_status": model_status,
        "model_type": structure_service.model_type,
        "timestamp": time.time()
    })

@app.route('/api/v1/structure/file', methods=['POST'])
@log_api_call
def structure_file():
    """处理上传的文件进行结构化解析"""
    if 'file' not in request.files:
        logger.warning("请求中没有文件")
        return jsonify({
            "status": "error",
            "message": "没有上传文件",
            "code": "NO_FILE"
        }), 400

    file = request.files['file']
    if file.filename == '':
        logger.warning("上传的文件名为空")
        return jsonify({
            "status": "error",
            "message": "文件名为空",
            "code": "EMPTY_FILENAME"
        }), 400

    # 检查文件类型
    if not allowed_file(file.filename):
        logger.warning(f"不支持的文件类型: {file.filename}")
        return jsonify({
            "status": "error",
            "message": f"不支持的文件类型，支持的格式: {', '.join(app.config['ALLOWED_EXTENSIONS'])}",
            "code": "UNSUPPORTED_FILE_TYPE"
        }), 400

    # 安全的文件名
    filename = secure_filename(file.filename)
    logger.info(f"处理上传文件: {filename}")

    # 保存临时文件
    temp_path = None
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as tmp_file:
            file.save(tmp_file.name)
            temp_path = tmp_file.name

        # 进行结构化解析
        results = structure_service.predict([temp_path])

        logger.info(f"文件 {filename} 解析完成")
        return jsonify({
            "status": "success",
            "message": "文档结构化解析完成",
            "filename": filename,
            "results": results
        })

    except Exception as e:
        logger.error(f"文件解析失败: {filename}, 错误: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"结构化解析失败: {str(e)}",
            "code": "PROCESSING_ERROR"
        }), 500

    finally:
        # 清理临时文件
        if temp_path and os.path.exists(temp_path):
            try:
                os.unlink(temp_path)
                logger.debug(f"清理临时文件: {temp_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")

@app.route('/api/v1/structure/url', methods=['POST'])
@log_api_call
def structure_url():
    """处理图片URL进行结构化解析"""
    data = request.get_json()
    if not data or 'urls' not in data:
        logger.warning("请求中没有提供URLs")
        return jsonify({
            "status": "error",
            "message": "请提供图片URL列表",
            "code": "NO_URLS"
        }), 400

    urls = data['urls']
    if not isinstance(urls, list):
        urls = [urls]

    if not urls:
        logger.warning("URL列表为空")
        return jsonify({
            "status": "error",
            "message": "URL列表不能为空",
            "code": "EMPTY_URL_LIST"
        }), 400

    logger.info(f"处理 {len(urls)} 个URL")

    try:
        # 进行结构化解析
        results = structure_service.predict(urls)

        logger.info(f"URL解析完成，处理了 {len(results)} 个结果")
        return jsonify({
            "status": "success",
            "message": "文档结构化解析完成",
            "url_count": len(urls),
            "results": results
        })

    except Exception as e:
        logger.error(f"URL解析失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"结构化解析失败: {str(e)}",
            "code": "PROCESSING_ERROR"
        }), 500

@app.route('/api/v1/structure/local', methods=['POST'])
@log_api_call
def structure_local():
    """处理本地文件路径进行结构化解析"""
    data = request.get_json()
    if not data or 'paths' not in data:
        logger.warning("请求中没有提供文件路径")
        return jsonify({
            "status": "error",
            "message": "请提供本地文件路径列表",
            "code": "NO_PATHS"
        }), 400

    paths = data['paths']
    if not isinstance(paths, list):
        paths = [paths]

    if not paths:
        logger.warning("文件路径列表为空")
        return jsonify({
            "status": "error",
            "message": "文件路径列表不能为空",
            "code": "EMPTY_PATH_LIST"
        }), 400

    # 检查文件是否存在
    missing_files = []
    for path in paths:
        if not os.path.exists(path):
            missing_files.append(path)

    if missing_files:
        logger.warning(f"文件不存在: {missing_files}")
        return jsonify({
            "status": "error",
            "message": f"以下文件不存在: {', '.join(missing_files)}",
            "missing_files": missing_files,
            "code": "FILE_NOT_FOUND"
        }), 400

    logger.info(f"处理 {len(paths)} 个本地文件")

    try:
        # 进行结构化解析
        results = structure_service.predict(paths)

        logger.info(f"本地文件解析完成，处理了 {len(results)} 个结果")
        return jsonify({
            "status": "success",
            "message": "文档结构化解析完成",
            "file_count": len(paths),
            "results": results
        })

    except Exception as e:
        logger.error(f"本地文件解析失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"结构化解析失败: {str(e)}",
            "code": "PROCESSING_ERROR"
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        "status": "error",
        "message": "API端点不存在",
        "code": "NOT_FOUND"
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    """405错误处理"""
    return jsonify({
        "status": "error",
        "message": "HTTP方法不允许",
        "code": "METHOD_NOT_ALLOWED"
    }), 405

@app.errorhandler(413)
def request_entity_too_large(error):
    """413错误处理"""
    return jsonify({
        "status": "error",
        "message": f"文件太大，最大允许 {app.config['MAX_CONTENT_LENGTH'] // (1024*1024)}MB",
        "code": "FILE_TOO_LARGE"
    }), 413

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='PP-StructureV3 OCR服务器')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=9999, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--config', default='default', help='配置环境 (default/development/production)')

    args = parser.parse_args()

    # 重新创建应用以使用指定配置
    global app
    app = create_app(args.config)
    logger = setup_logger('pp_structure_v3', app.config)

    logger.info("启动PP-StructureV3文档结构化解析服务...")
    logger.info(f"服务地址: http://{args.host}:{args.port}")
    logger.info("API文档:")
    logger.info("  GET  /              - 服务信息")
    logger.info("  GET  /health        - 健康检查")
    logger.info("  POST /api/v1/structure/file  - 上传文件结构化解析")
    logger.info("  POST /api/v1/structure/url   - URL图片结构化解析")
    logger.info("  POST /api/v1/structure/local - 本地文件结构化解析")

    # 预初始化模型（可选）
    if not structure_service.init_model():
        logger.warning("模型初始化失败，将在首次请求时重试")

    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug or app.config.get('DEBUG', False)
    )
