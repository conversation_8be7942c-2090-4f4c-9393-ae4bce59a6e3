# PPStructureV3 OCR识别服务

基于Flask和PaddleOCR/PaddleX的文档结构化解析服务，支持图像OCR识别、版面分析等功能。

## 🚀 功能特性

- **多种输入方式**: 支持文件上传、URL链接、本地文件路径
- **智能OCR识别**: 基于PaddleOCR/PaddleX的高精度文字识别
- **结构化解析**: 提供版面分析、表格识别等功能
- **RESTful API**: 标准的HTTP API接口
- **完善的日志**: 详细的请求和错误日志记录
- **配置灵活**: 支持多环境配置和参数调整
- **错误处理**: 完善的异常处理和错误响应

## 📋 系统要求

- Python 3.8+
- 支持的操作系统: Windows, macOS, Linux
- 内存: 建议4GB以上
- 存储: 建议10GB以上可用空间（用于模型文件）

## 🛠️ 安装部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd PPStructureV3Server
```

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

### 3. 安装依赖

#### 方法一：智能安装（推荐）
```bash
make install-smart
# 或者
python install_dependencies.py
```

#### 方法二：标准安装
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

#### 方法三：核心依赖安装（如果完整安装失败）
```bash
pip install -r requirements-core.txt
```

### 4. 启动服务

#### 使用启动脚本（推荐）

```bash
# Linux/macOS
./start_server.sh

# Windows
start_server.bat
```

#### 手动启动

```bash
python pp_structure_v3_server.py
```

#### 自定义参数启动

```bash
python pp_structure_v3_server.py --host 0.0.0.0 --port 8080 --debug
```

## 🔧 配置说明

### 环境变量配置

创建 `.env` 文件来配置环境变量：

```env
# 服务配置
HOST=0.0.0.0
PORT=9999
DEBUG=True

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads

# OCR模型配置
USE_ANGLE_CLS=True
OCR_LANG=ch
USE_GPU=False

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=pp_structure_v3.log

# 安全配置
SECRET_KEY=your-secret-key-here
ENABLE_CORS=True
```

### 配置文件

支持多环境配置：
- `development`: 开发环境
- `production`: 生产环境
- `testing`: 测试环境

## 📚 API文档

### 基础接口

#### 1. 服务信息
```http
GET /
```

#### 2. 健康检查
```http
GET /health
```

### OCR识别接口

#### 1. 文件上传识别
```http
POST /api/v1/structure/file
Content-Type: multipart/form-data

file: <图片文件>
```

#### 2. URL图片识别
```http
POST /api/v1/structure/url
Content-Type: application/json

{
  "urls": ["http://example.com/image1.jpg", "http://example.com/image2.jpg"]
}
```

#### 3. 本地文件识别
```http
POST /api/v1/structure/local
Content-Type: application/json

{
  "paths": ["/path/to/image1.jpg", "/path/to/image2.jpg"]
}
```

### 响应格式

#### 成功响应
```json
{
  "status": "success",
  "message": "文档结构化解析完成",
  "results": [
    {
      "image_path": "/path/to/image.jpg",
      "layout": [
        {
          "type": "text",
          "bbox": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]]
        }
      ],
      "ocr": ["识别的文字内容1", "识别的文字内容2"],
      "confidence": [0.95, 0.87],
      "table": [],
      "raw_ocr_result": {...}
    }
  ]
}
```

#### 错误响应
```json
{
  "status": "error",
  "message": "错误描述",
  "code": "ERROR_CODE"
}
```

## 🧪 测试

### 运行API测试

```bash
python test_api.py
```

### 自定义测试

```bash
# 指定服务器地址
python test_api.py --url http://localhost:8080

# 指定测试图片
python test_api.py --image /path/to/test.jpg

# 测试URL处理
python test_api.py --test-urls http://example.com/image.jpg

# 测试本地文件
python test_api.py --test-local /path/to/image1.jpg /path/to/image2.jpg
```

## 📁 项目结构

```
PPStructureV3Server/
├── pp_structure_v3_server.py  # 主服务文件
├── config.py                  # 配置文件
├── logger.py                  # 日志配置
├── test_api.py               # API测试脚本
├── requirements.txt          # Python依赖
├── start_server.sh          # Linux/macOS启动脚本
├── start_server.bat         # Windows启动脚本
├── README.md                # 项目文档
├── .env                     # 环境变量配置（需要创建）
├── venv/                    # Python虚拟环境
├── uploads/                 # 文件上传目录
└── logs/                    # 日志文件目录
```

## 🔍 故障排除

### 常见问题

1. **依赖安装失败**
   - **PaddleX版本问题**: 使用 `make install-smart` 或 `python install_dependencies.py` 智能安装
   - **网络问题**: 尝试使用国内镜像源：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`
   - **版本冲突**: 使用核心依赖安装：`pip install -r requirements-core.txt`
   - **权限问题**: 在命令前加 `sudo`（Linux/macOS）或以管理员身份运行（Windows）

2. **模型初始化失败**
   - 检查网络连接，确保能下载模型文件
   - 检查磁盘空间是否充足（建议10GB以上）
   - 尝试手动安装PaddleOCR: `pip install paddleocr`
   - 如果PaddleX安装失败，服务会自动降级使用PaddleOCR

3. **文件上传失败**
   - 检查文件大小是否超过限制（默认16MB）
   - 检查文件格式是否支持
   - 检查uploads目录权限

4. **服务启动失败**
   - 检查端口是否被占用：`lsof -i :9999`（Linux/macOS）或 `netstat -ano | findstr :9999`（Windows）
   - 检查Python版本是否符合要求（3.8+）
   - 查看详细错误日志

### 日志查看

```bash
# 查看实时日志
tail -f pp_structure_v3.log

# 查看错误日志
grep ERROR pp_structure_v3.log
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**注意**: 首次运行时，系统会自动下载PaddleOCR模型文件，可能需要较长时间，请耐心等待。
